"""
Enumeration classes for validation system
"""

from enum import Enum


class ValidationType(Enum):
    """Enumeration for validation types"""
    REGEX = "REGEX"
    REGEX_LIST = "REGEX_LIST"
    EXPRESSION_TYPE = "EXPRESSION_TYPE"
    EXPRESSION_TYPE_LIST = "EXPRESSION_TYPE_LIST"


class ValidationResult(Enum):
    """Enumeration for validation results"""
    VALID = "VALID"
    INVALID = "INVALID"
    ERROR = "ERROR"


class ConditionType(Enum):
    """Enumeration for condition types in multi-rule validation"""
    AND = "AND"
    OR = "OR"


class FieldType(Enum):
    """Enumeration for field data types"""
    STRING = "string"
    INTEGER = "integer"
    DECIMAL = "decimal"
    BOOLEAN = "boolean"
    DATE = "date"
    OBJECT = "object"
