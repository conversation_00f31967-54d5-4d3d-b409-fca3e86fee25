#!/usr/bin/env python3
"""
<PERSON>ript to update the database with the cleaned collection_data.json
"""

import json
from db_utils import DatabaseClient

def update_database_with_clean_config():
    """Update the database with the cleaned collection_data.json"""
    print("Updating database with cleaned validation configuration...")
    
    try:
        # Load the cleaned collection_data.json
        with open('collection_data.json', 'r') as f:
            clean_config = json.load(f)
        
        print(f"Loaded clean config with validation_type_key: {clean_config.get('validation_type_key')}")
        
        # Connect to database
        client = DatabaseClient()
        if not client.connect():
            print("❌ Failed to connect to database")
            return False
        
        # Get the validation_type_key
        validation_key = clean_config.get("validation_type_key", "tag_titles")
        
        # Update the document in the database
        try:
            # Try to update by validation_type_key first
            result = client.collection.replace_one(
                {"validation_type_key": validation_key},
                clean_config,
                upsert=True
            )
            
            if result.matched_count > 0:
                print(f"✅ Updated existing document with validation_type_key: {validation_key}")
            elif result.upserted_id:
                print(f"✅ Inserted new document with validation_type_key: {validation_key}")
            else:
                print("❌ No document was updated or inserted")
                return False
            
            # Verify the update
            verification = client.get_config(validation_key)
            if verification:
                # Count validation types in the updated config
                type_counts = {"REGEX": 0, "REGEX_LIST": 0, "EXPRESSION_TYPE": 0, "EXPRESSION_TYPE_LIST": 0, "OTHER": 0}
                
                fields = verification.get("properties", {}).get("fields", {})
                for field_name, field_config in fields.items():
                    validation_rules = field_config.get("validation_rules", [])
                    for rule in validation_rules:
                        validation_type = rule.get("isValidationType", "")
                        if validation_type in type_counts:
                            type_counts[validation_type] += 1
                        else:
                            type_counts["OTHER"] += 1
                
                print(f"\n✅ Database update verified!")
                print(f"Validation type distribution in database:")
                for vtype, count in type_counts.items():
                    print(f"  {vtype}: {count}")
                
                if type_counts["OTHER"] == 0:
                    print(f"\n🎉 SUCCESS: Database now contains only the 4 supported validation types!")
                    return True
                else:
                    print(f"\n⚠️  WARNING: Database still contains {type_counts['OTHER']} unsupported validation types")
                    return False
            else:
                print("❌ Failed to verify database update")
                return False
                
        except Exception as e:
            print(f"❌ Error updating database: {e}")
            return False
        
        finally:
            client.close()
    
    except FileNotFoundError:
        print("❌ collection_data.json file not found")
        return False
    except json.JSONDecodeError:
        print("❌ Invalid JSON in collection_data.json")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_fallback_config_loader():
    """Create a modified index.py that uses local config as fallback"""
    print("\nCreating fallback configuration loader...")
    
    fallback_code = '''
def _extract_validation_config(self, config_key: str, config: Dict = None) -> Dict:
    """Extract validation configuration from database or local file as fallback"""
    if config:
        if config.get("validation_type_key") == config_key:
            return config.get("properties", {})

        properties = config.get("properties", {})
        if config_key in properties:
            return properties[config_key]

    try:
        # Try database first
        db_config = self.db_client.get_config(config_key)
        if db_config:
            if db_config.get("validation_type_key") == config_key:
                return db_config.get("properties", {})

            properties = db_config.get("properties", {})
            if config_key in properties:
                return properties[config_key]

            return db_config.get("properties", {})

    except Exception as e:
        print(f"Error fetching config from database: {e}")
    
    # Fallback to local collection_data.json
    try:
        print(f"Falling back to local collection_data.json for key: {config_key}")
        with open("collection_data.json", "r") as f:
            local_config = json.load(f)
        
        if local_config.get("validation_type_key") == config_key:
            return local_config.get("properties", {})
        
        properties = local_config.get("properties", {})
        if config_key in properties:
            return properties[config_key]
            
        return local_config.get("properties", {})
        
    except Exception as e:
        print(f"Error loading local config: {e}")
        return {}
'''
    
    print("Add this method to your WorksheetValidator class in index.py:")
    print("=" * 60)
    print(fallback_code)
    print("=" * 60)

def main():
    """Main function"""
    print("Database Configuration Update Tool")
    print("=" * 50)
    
    # Try to update the database
    success = update_database_with_clean_config()
    
    if not success:
        print("\n" + "=" * 50)
        print("Database update failed. Here are your options:")
        print("\n1. Fix database connection and try again")
        print("2. Use local collection_data.json as fallback")
        
        create_fallback_config_loader()
    
    print("\n" + "=" * 50)
    print("Next steps:")
    print("1. Run 'python index.py' to test the validation")
    print("2. Check that only 4 validation types are used")
    print("3. Verify no CROSS_FIELD, NUMERIC, DATE, or BOOLEAN errors")

if __name__ == "__main__":
    main()
