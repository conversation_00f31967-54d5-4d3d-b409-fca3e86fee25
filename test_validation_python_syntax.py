#!/usr/bin/env python3
"""
Test script to verify that the validation system works with pure Python syntax
without JavaScript-to-Python conversion logic.
"""

import json
from validation_utils import ValidationUtils

def test_python_syntax_validation():
    """Test that validation works with pure Python syntax"""
    
    # Load the collection data
    with open('collection_data.json', 'r') as f:
        config = json.load(f)
    
    # Initialize validation utils
    validator = ValidationUtils()
    
    # Test data with matching VIN across documents
    test_data = {
        "groups": {
            "bos": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_name": {"value": "John Doe"},
                    "odometer_reading": {"value": 50000},
                    "tavt_tax_amount": {"value": 1500.00}
                }
            },
            "mvl": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_full_name": {"value": "<PERSON>"},
                    "odometer_reading": {"value": 50005},
                    "tavt_tax_amount": {"value": 1450.00}
                }
            },
            "title": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_name": {"value": "John Doe"},
                    "odometer_reading": {"value": 50002}
                }
            },
            "red_reassignment": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_name": {"value": "John Doe"},
                    "odometer_reading": {"value": 50001}
                }
            },
            "dl": {
                "fields": {
                    "full_name": {"value": "John Doe"}
                }
            },
            "dealer_dmv": {
                "fields": {
                    "tavt_tax_amount": {"value": 1480.00}
                }
            }
        }
    }
    
    print("Testing VIN consistency validation...")
    # Test VIN field validation
    vin_field_config = config["properties"]["fields"]["vin"]
    vin_errors = validator.validate_field("1HGBH41JXMN109186", vin_field_config, config, test_data, "bos", "vin")
    print(f"VIN validation errors: {vin_errors}")
    
    print("\nTesting buyer name consistency validation...")
    # Test buyer name field validation
    buyer_name_config = config["properties"]["fields"]["buyer_name"]
    buyer_name_errors = validator.validate_field("John Doe", buyer_name_config, config, test_data, "bos", "buyer_name")
    print(f"Buyer name validation errors: {buyer_name_errors}")
    
    print("\nTesting odometer reading validation...")
    # Test odometer reading validation
    odometer_config = config["properties"]["fields"]["odometer_reading"]
    odometer_errors = validator.validate_field(50000, odometer_config, config, test_data, "bos", "odometer_reading")
    print(f"Odometer validation errors: {odometer_errors}")
    
    print("\nTesting TAVT tax amount validation...")
    # Test TAVT tax amount validation
    tavt_config = config["properties"]["fields"]["tavt_tax_amount"]
    tavt_errors = validator.validate_field(1500.00, tavt_config, config, test_data, "bos", "tavt_tax_amount")
    print(f"TAVT validation errors: {tavt_errors}")
    
    print("\nTesting boolean validation...")
    # Test boolean validation
    lien_satisfied_config = config["properties"]["fields"]["lien_satisfied"]
    lien_errors = validator.validate_field(True, lien_satisfied_config, config, test_data, "bos", "lien_satisfied")
    print(f"Lien satisfied validation errors: {lien_errors}")
    
    print("\nTesting seller signature validation...")
    # Test seller signature validation
    seller_sig_config = config["properties"]["fields"]["seller_signature"]
    seller_sig_errors = validator.validate_field(True, seller_sig_config, config, test_data, "bos", "seller_signature")
    print(f"Seller signature validation errors: {seller_sig_errors}")
    
    print("\n=== Test completed successfully! ===")
    print("All validations are now using pure Python syntax without JavaScript conversion.")

if __name__ == "__main__":
    test_python_syntax_validation()
