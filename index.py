# -*- coding: utf-8 -*-
import re
import json
from typing import Dict, Any, <PERSON><PERSON>
from db_utils import DatabaseClient
from validation_utils import ValidationUtils


class WorksheetValidator:
    """Main validator class that orchestrates worksheet validation using ValidationUtils"""

    def __init__(self, db_client: DatabaseClient = None):
        self.config_data = None
        self.db_client = db_client or DatabaseClient()
        self.validation_utils = ValidationUtils()

    def validate_worksheet_data(self, data: dict, config_key: str, config: Dict) -> Tuple[bool, dict]:
        """Validate worksheet data against configuration rules"""
        config_data = self._extract_validation_config(config_key, config)
        if not config_data:
            return False, {"error": f"Configuration not found for key: {config_key}"}

        updated_data = data.copy()
        all_valid = True

        groups = data.get("groups", {})

        for group_name, group_data in groups.items():
            if "bre_exceptions" not in updated_data["groups"][group_name]:
                updated_data["groups"][group_name]["bre_exceptions"] = {}

            fields = group_data.get("fields", {})

            for field_name, field_data in fields.items():
                field_value = field_data.get("value") if isinstance(field_data, dict) else field_data

                field_config = self._find_field_config(field_name, config_data)
                if not field_config:
                    continue

                field_errors = self.validation_utils.validate_field(
                    field_value, field_config, config_data, data, group_name, field_name
                )

                if field_errors:
                    all_valid = False
                    updated_data["groups"][group_name]["bre_exceptions"][field_name] = "; ".join(field_errors)

        return all_valid, updated_data

    def _extract_validation_config(self, config_key: str, config: Dict = None) -> Dict:
        """Extract validation configuration from database or loaded config data"""
        if config:
            if config.get("validation_type_key") == config_key:
                return config.get("properties", {})

            properties = config.get("properties", {})
            if config_key in properties:
                return properties[config_key]

        try:
            db_config = self.db_client.get_config(config_key)
            if not db_config:
                print(f"Configuration not found in database for key: {config_key}")
                return {}

            if db_config.get("validation_type_key") == config_key:
                return db_config.get("properties", {})

            properties = db_config.get("properties", {})
            if config_key in properties:
                return properties[config_key]

            return db_config.get("properties", {})

        except Exception as e:
            print(f"Error fetching config from database: {e}")
            return {}

    def _normalize_field_name(self, field_name: str) -> str:
        """Normalize field names by removing multiple underscores"""
        normalized = re.sub(r'_{2,}', '_', field_name)
        return normalized

    def _find_field_config(self, field_name: str, config: Dict) -> Dict:
        """Find field configuration in the config data"""
        fields_config = config.get("fields", {})

        if field_name in fields_config:
            return fields_config[field_name]

        normalized_name = self._normalize_field_name(field_name)
        if normalized_name != field_name and normalized_name in fields_config:
            return fields_config[normalized_name]

        return None


def example_worksheet_usage():
    """Example usage of the WorksheetValidator"""
    try:
        with open("test_workSheet.json", "r") as f:
            worksheet_data = json.load(f)

        client = DatabaseClient()
        validator = WorksheetValidator(client)

        # Load local config file
        with open("collection_data.json", "r") as f:
            config_data = json.load(f)

        key = "tag_titles"
        is_valid, updated_data = validator.validate_worksheet_data(worksheet_data, key, config_data)

        if is_valid:
            print("✅ Worksheet validation passed!")
        else:
            print("❌ Worksheet validation failed. Check bre_exceptions in groups:")

            for group_name, group_data in updated_data.get("groups", {}).items():
                exceptions = group_data.get("bre_exceptions", {})
                if exceptions:
                    print(f"\n  Group '{group_name}' exceptions:")
                    for field, error in exceptions.items():
                        print(f"    - {field}: {error}")

        with open("validated_output.json", "w") as f:
            json.dump(updated_data, f, indent=2)
        print(f"\n💾 Updated data with bre_exceptions saved to 'validated_output.json'")

        return updated_data

    except FileNotFoundError:
        print("❌ test_workSheet.json file not found")
        return None
    except json.JSONDecodeError:
        print("❌ Invalid JSON in test_workSheet.json")
        return None


if __name__ == "__main__":
    example_worksheet_usage()