# Direct Expression Validation System

## Overview

The validation system now supports **direct JavaScript-like expressions** for maximum flexibility and config-driven validation. You can write expressions exactly like you would in JavaScript, and the system handles the evaluation.

## Key Features

### ✅ **Direct Expression Syntax**
Write expressions using familiar JavaScript syntax:
```json
{
  "expression": "mv1.value === dealer_dmv.value && bos.value === dealer_dmv.value"
}
```

### ✅ **Array Support for Multiple Expressions**
Use arrays for both expressions and error messages:
```json
{
  "expressions": [
    "dealer_dmv.county.value !== null",
    "mv1.county.value !== null", 
    "dealer_dmv.county.value === mv1.county.value"
  ],
  "error_msgs": [
    "Dealer DMV county is required",
    "MV1 county is required",
    "Counties must match"
  ]
}
```

### ✅ **Array Support for Regex Patterns**
Multiple regex patterns with individual error messages:
```json
{
  "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"],
  "error_msgs": ["Value is required", "Value should be alphanumeric"]
}
```

## Expression Syntax

### Field References
- **Cross-group fields**: `group.field.value` (e.g., `mv1.county.value`, `bos.buyer_name.value`)
- **Current field**: `value` (refers to the field being validated)
- **Nested fields**: `group.field.subfield.value`

### Operators
- **Equality**: `===` (strict equal), `!==` (strict not equal)
- **Comparison**: `>`, `<`, `>=`, `<=`
- **Logical**: `&&` (AND), `||` (OR)
- **Null checks**: `!== null`, `=== null`
- **Empty checks**: `!== ""`, `=== ""`

### Math Functions
- **Absolute value**: `Math.abs(value1 - value2)`
- **Maximum**: `Math.max(value1, value2)`
- **Minimum**: `Math.min(value1, value2)`

### Condition Types
- **AND**: All expressions must be true
- **OR**: At least one expression must be true

## Examples

### 1. Simple Field Equality
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "mv1.year.value === bos.year.value",
  "error_msg": "Year must match between MV1 and BOS"
}
```

### 2. Multiple Field Consistency
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "bos.year.value === mv1.year.value && mv1.year.value === title.year.value",
  "error_msg": "Year must match across all documents"
}
```

### 3. Conditional Validation
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "lien_holder.value === null || lien_holder.value === \"\" || (lien_address.value !== null && lien_address.value !== \"\")",
  "error_msg": "When lien holder is specified, address is required"
}
```

### 4. Numeric Range Validation
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "value >= 16 && value <= 120",
  "error_msg": "Age must be between 16 and 120"
}
```

### 5. Tolerance-based Validation
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "Math.abs(bos.tax_amount.value - mv1.tax_amount.value) <= 500",
  "error_msg": "Tax amounts must be within $500 tolerance"
}
```

### 6. Multiple Expressions with Individual Error Messages
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expressions": [
    "dealer_dmv.county.value !== null && dealer_dmv.county.value !== \"\"",
    "mv1.county.value !== null && mv1.county.value !== \"\"",
    "dealer_dmv.county.value === mv1.county.value"
  ],
  "error_msgs": [
    "Dealer DMV county is required",
    "MV1 county is required", 
    "Counties must match between Dealer DMV and MV1"
  ],
  "conditionType": "AND"
}
```

### 7. OR Logic with Multiple Options
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "(primary_address.value !== null && primary_address.value !== \"\") || (secondary_address.value !== null && secondary_address.value !== \"\")",
  "error_msg": "Either primary or secondary address must be provided"
}
```

### 8. Complex Business Logic
```json
{
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "title.odometer.value < bos.odometer.value || title.odometer.value === bos.odometer.value",
  "error_msg": "Title odometer may be less than or equal to BOS odometer reading"
}
```

## Regex Array Examples

### 1. Multiple Patterns with AND Logic
```json
{
  "isValidationType": "REGEX",
  "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"],
  "error_msgs": ["Value is required", "Value should be alphanumeric"],
  "conditionType": "AND"
}
```

### 2. Multiple Patterns with OR Logic
```json
{
  "isValidationType": "REGEX",
  "regexes": ["^[A-Za-z\\s]+$", "^[0-9]+$"],
  "error_msgs": ["Value should be letters only", "Value should be numbers only"],
  "conditionType": "OR"
}
```

## Configuration Structure

### Single Expression
```json
{
  "check": "validation_name",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "your_expression_here",
  "error_msg": "Error message"
}
```

### Multiple Expressions
```json
{
  "check": "validation_name",
  "isValidationType": "EXPRESSION_TYPE",
  "expressions": ["expr1", "expr2", "expr3"],
  "error_msgs": ["error1", "error2", "error3"],
  "conditionType": "AND"
}
```

### Single Regex
```json
{
  "check": "validation_name",
  "isValidationType": "REGEX",
  "regex": "^pattern$",
  "error_msg": "Error message"
}
```

### Multiple Regex
```json
{
  "check": "validation_name",
  "isValidationType": "REGEX",
  "regexes": ["^pattern1$", "^pattern2$"],
  "error_msgs": ["error1", "error2"],
  "conditionType": "AND"
}
```

## Benefits

### 1. **Maximum Flexibility**
- Write any validation logic you can think of
- No need to create new validation types
- Direct JavaScript-like syntax

### 2. **Config-Driven**
- All validation logic in JSON configuration
- No code changes needed for new validation rules
- Easy to maintain and update

### 3. **Precise Error Messages**
- Individual error messages for each expression
- Specific feedback for each validation failure
- Better user experience

### 4. **Performance**
- Single evaluation function handles all cases
- No complex switch statements
- Efficient expression processing

### 5. **Maintainability**
- Clean, readable expressions
- Self-documenting validation logic
- Easy to debug and test

## Migration from Old System

### Old Global Function Approach
```json
{
  "expression": "fieldsMatch({{bos.year}}, {{mv1.year}})",
  "error_msg": "Years must match"
}
```

### New Direct Expression Approach
```json
{
  "expression": "bos.year.value === mv1.year.value",
  "error_msg": "Years must match"
}
```

The new approach is more intuitive and doesn't require learning custom function names!
