"""
Simple database service for validation system
"""

import json
from typing import Dict, Any, Optional
from datetime import datetime
from .config import Config


class DatabaseService:
    """Simple database service that handles data operations"""
    
    def __init__(self):
        """Initialize database service"""
        self.config = Config()
        self.connection = None
        self._connect()
    
    def _connect(self):
        """Connect to database based on configuration"""
        try:
            if self.config.DB_TYPE.lower() == 'mongodb':
                self._connect_mongodb()
            else:
                print(f"Database type {self.config.DB_TYPE} not implemented yet")
                self.connection = None
        except Exception as e:
            print(f"Database connection failed: {str(e)}")
            self.connection = None
    
    def _connect_mongodb(self):
        """Connect to MongoDB"""
        try:
            from pymongo import MongoClient
            
            self.client = MongoClient(self.config.MONGO_URI)
            self.db = self.client[self.config.MONGO_DB_NAME]
            self.collection = self.db[self.config.MONGO_COLLECTION_NAME]
            self.connection = True
            print(f"Connected to MongoDB: {self.config.MONGO_URI}")
            
        except ImportError:
            print("Warning: pymongo not installed. Database features disabled.")
            self.connection = None
        except Exception as e:
            print(f"MongoDB connection failed: {str(e)}")
            self.connection = None
    
    def get_validation_config(self) -> Dict[str, Any]:
        """
        Get validation configuration from database or fallback to file
        
        Returns:
            Validation configuration dictionary
        """
        if self.connection and self.config.DB_TYPE.lower() == 'mongodb':
            try:
                # Try to get from database first
                result = self.collection.find_one({'validation_type_key': {'$exists': True}})
                if result:
                    result.pop('_id', None)  # Remove MongoDB _id field
                    return result
            except Exception as e:
                print(f"Error fetching config from database: {str(e)}")
        
        # Fallback to collection_data.json file
        try:
            with open('collection_data.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading collection_data.json: {str(e)}")
            return {}
    
    def save_validation_results(self, worksheet_id: str, results: Dict[str, Any]) -> bool:
        """
        Save validation results to database
        
        Args:
            worksheet_id: Unique identifier for the worksheet
            results: Validation results dictionary
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection:
            print("No database connection available")
            return False
        
        try:
            if self.config.DB_TYPE.lower() == 'mongodb':
                document = {
                    'worksheet_id': worksheet_id,
                    'validation_results': results,
                    'created_at': datetime.utcnow(),
                    'status': 'completed'
                }
                
                result = self.collection.insert_one(document)
                success = result.inserted_id is not None
                if success:
                    print(f"Validation results saved to database with ID: {result.inserted_id}")
                return success
            
        except Exception as e:
            print(f"Error saving validation results: {str(e)}")
            return False
    
    def get_worksheet_data(self, worksheet_id: str) -> Optional[Dict[str, Any]]:
        """
        Get worksheet data from database
        
        Args:
            worksheet_id: Unique identifier for the worksheet
            
        Returns:
            Worksheet data dictionary or None if not found
        """
        if not self.connection:
            return None
        
        try:
            if self.config.DB_TYPE.lower() == 'mongodb':
                result = self.collection.find_one({'worksheet_id': worksheet_id})
                if result:
                    result.pop('_id', None)  # Remove MongoDB _id field
                    return result
                    
        except Exception as e:
            print(f"Error fetching worksheet data: {str(e)}")
        
        return None
    
    def close(self):
        """Close database connection"""
        try:
            if hasattr(self, 'client') and self.client:
                self.client.close()
                print("Database connection closed")
        except Exception as e:
            print(f"Error closing database connection: {str(e)}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
