{"validation_type_key": "tag_titles", "properties": {"fields": {"deal_number": {"required": true, "type": "string", "validation_rules": [{"check": "deal_number_validation", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"], "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"], "conditionType": "AND"}]}, "stock_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9-]{1,20}$", "error_msg": "Stock number is required and should be alphanumeric with hyphens (1-20 characters)"}]}, "vin": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_vin_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-HJ-NPR-Z0-9]{17}$"], "error_msgs": ["VIN is required", "VIN should be exactly 17 characters (excluding I, O, Q)"], "conditionType": "AND"}, {"check": "vin_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.vin.value == mvl.vin.value and mvl.vin.value == title.vin.value and title.vin.value == red_reassignment.vin.value", "error_msg": "VIN number does not match across documents"}]}, "year": {"required": true, "type": "integer", "validation_rules": [{"check": "valid_year", "isValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030"}, {"check": "year_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.year.value == mvl.year.value and mvl.year.value == title.year.value and title.year.value == red_reassignment.year.value", "error_msg": "Year does not match across documents"}]}, "make": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"}, {"check": "make_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.make.value == mvl.make.value and mvl.make.value == title.make.value and title.make.value == red_reassignment.make.value", "error_msg": "Make does not match across documents"}]}, "model": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"}, {"check": "model_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.model.value == mvl.model.value and mvl.model.value == title.model.value and title.model.value == red_reassignment.model.value", "error_msg": "Model does not match across documents"}]}, "odometer_reading": {"required": true, "type": "integer", "validation_rules": [{"check": "odometer_range", "isValidationType": "REGEX", "regex": "^(0|[1-9][0-9]{0,5})$", "error_msg": "Odometer reading should be a positive number between 0 and 999,999"}, {"check": "odometer_consistency_odometer_title_exception", "isValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["abs(bos.odometer_reading.value - mvl.odometer_reading.value) <= 10 and abs(mvl.odometer_reading.value - red_reassignment.odometer_reading.value) <= 10 and abs(red_reassignment.odometer_reading.value - title.odometer_reading.value) <= 10", "title.odometer_reading.value <= bos.odometer_reading.value"], "error_msgs": ["Odometer reading does not match across documents", "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"], "conditionType": "AND"}]}, "buyer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_name_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Buyer name is required", "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND"}, {"check": "buyer_name_consistency", "isValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["bos.buyer_name.value != None and bos.buyer_name.value != \"\"", "dl.full_name.value != None and dl.full_name.value != \"\"", "mvl.buyer_full_name.value != None and mvl.buyer_full_name.value != \"\"", "bos.buyer_name.value == dl.full_name.value and dl.full_name.value == mvl.buyer_full_name.value and mvl.buyer_full_name.value == red_reassignment.buyer_name.value and red_reassignment.buyer_name.value == title.buyer_name.value"], "error_msgs": ["BOS buyer name is required", "DL full name is required", "MVL buyer full name is required", "Buyer name does not match across documents"], "conditionType": "AND"}]}, "co_buyer_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Co-buyer name should contain only letters, spaces, hyphens and apostrophes"}, {"check": "co_buyer_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.co_buyer_name.value == mvl.co_buyer_name.value", "error_msg": "Co-buyer name does not match across documents"}]}, "buyer_address": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer address is required"}, {"check": "address_consistency_lien_address_consistency", "isValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["true", "buyer_address.value == mvl.lien_holder_address.value"], "error_msgs": ["Buyer address consistency check disabled", "Buyer address does not match lien holder address in MV1"], "conditionType": "AND"}]}, "sale_price": {"required": true, "type": "decimal", "validation_rules": [{"check": "sale_price_range", "isValidationType": "REGEX", "regex": "^(0\\.(0[1-9]|[1-9][0-9]?)|[1-9][0-9]{0,5}(\\.[0-9]{1,2})?|999999(\\.99?)?)$", "error_msg": "Sale price should be a positive amount between $0.01 and $999,999.99"}]}, "trade_in_value": {"required": false, "type": "decimal", "validation_rules": [{"check": "trade_in_value_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$", "error_msg": "Trade-in value should be a positive amount up to $999,999.99"}, {"check": "trade_in_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "True", "error_msg": "Trade-in value does not match between Bill of Sale and Red Reassignment"}]}, "tavt_tax_amount": {"required": true, "type": "decimal", "validation_rules": [{"check": "tavt_tax_amount_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "error_msg": "TAVT tax amount is required and should be between $0 and $99,999.99"}, {"check": "tavt_consistency_tavt_calculation_verification", "isValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["abs(bos.tavt_tax_amount.value - mvl.tavt_tax_amount.value) <= 500 and abs(mvl.tavt_tax_amount.value - dealer_dmv.tavt_tax_amount.value) <= 500", "bos.field.value == mvl.field.value and mvl.field.value == dealer_dmv.field.value"], "error_msgs": ["TAVT tax amount differs by more than $500 across documents - requires manual review", "TAVT calculation does not match expected amount based on sale price and county tax rate"], "conditionType": "AND"}]}, "total_amount_due": {"required": false, "type": "decimal", "validation_rules": [{"check": "total_amount_due_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$", "error_msg": "Total amount due should be a positive amount up to $999,999.99"}, {"check": "total_amount_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.field.value == red_reassignment.field.value and red_reassignment.field.value == dealer_dmv.field.value", "error_msg": "Total amount due does not match across funding and documents"}]}, "lien_holder_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Lien holder name should be 2-100 characters with valid business name characters"}, {"check": "lien_holder_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.field.value == mvl.field.value and mvl.field.value == title.field.value", "error_msg": "Lien holder name does not match across documents"}]}, "full_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_name_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Full name is required", "Full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND"}, {"check": "full_name_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.buyer_name.value == mvl.buyer_full_name.value", "error_msg": "Full name does not match across documents"}]}, "date_of_birth": {"required": true, "type": "date", "validation_rules": [{"check": "dob_format_check_dob_age_16_to_120_check", "isValidationType": "REGEX_LIST", "regexes": ["^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-9])$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-8])$"], "error_msgs": ["Date of birth is required and should be in MM/DD/YYYY format with a valid year between 1900 and 2009", "Date of birth should be in MM/DD/YYYY format and indicate an age between 16 and 120 years"], "conditionType": "AND"}]}, "address__street": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_address_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$"], "error_msgs": ["Street address is required", "Street address should start with a number followed by street name (1-100 characters)"], "conditionType": "AND"}]}, "city": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_city_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["City is required", "City should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND"}]}, "state": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_state_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Z]{2}$"], "error_msgs": ["State is required", "State should be a 2-letter abbreviation (e.g., GA, FL)"], "conditionType": "AND"}]}, "zip": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_zip_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[0-9]{5}(-[0-9]{4})?$"], "error_msgs": ["ZIP code is required", "ZIP code should be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)"], "conditionType": "AND"}]}, "driver_s_license_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_license_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Driver's license number is required", "Driver's license number should be 6-20 alphanumeric characters"], "conditionType": "AND"}, {"check": "license_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.field.value == mvl.field.value and mvl.field.value == red_reassignment.customer_id.value", "error_msg": "Driver's license number does not match across documents"}]}, "buyer_full_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_name_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["Buyer full name is required", "Buyer full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND"}, {"check": "full_name_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.buyer_name.value == mvl.buyer_full_name.value and mvl.buyer_full_name.value == dl.full_name.value", "error_msg": "Buyer full name does not match across documents"}]}, "county_of_residence": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_county_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z\\s'-]{2,50}$"], "error_msgs": ["County of residence is required", "County should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"], "conditionType": "AND"}]}, "customer_id": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_license_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Customer ID (Driver's License Number) is required", "Customer ID should be 6-20 alphanumeric characters"], "conditionType": "AND"}, {"check": "customer_id_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "true", "error_msg": "Customer ID does not match across documents"}]}, "body_style": {"required": false, "type": "string", "validation_rules": [{"check": "body_style_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,20}$", "error_msg": "Body style should contain only letters, numbers, spaces, and hyphens (1-20 characters)"}]}, "lien_holder_address": {"required": false, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Lien holder address is required"}]}, "dealer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_dealer_name_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Dealer name is required", "Dealer name should be 2-100 characters with valid business name characters"], "conditionType": "AND"}]}, "dealer_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_dealer_number_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{3,20}$"], "error_msgs": ["Dealer number is required", "Dealer number should be 3-20 alphanumeric characters"], "conditionType": "AND"}]}, "odometer_type": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_odometer_type_valid", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(Actual|Exceeds|Not Actual)$"], "error_msgs": ["Odometer type is required", "Odometer type should be 'Actual', 'Exceeds', or 'Not Actual'"], "conditionType": "AND"}]}, "date_of_reassignment": {"required": true, "type": "date", "validation_rules": [{"check": "reassignment_date_format_and_year_reassignment_date_required", "isValidationType": "REGEX_LIST", "regexes": ["^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$", "^.+$"], "error_msgs": ["Date of reassignment should be in MM/DD/YYYY format and between 2000 and 2025", "Date of reassignment is required"], "conditionType": "AND"}, {"check": "date_sequence", "isValidationType": "EXPRESSION_TYPE", "expression": "true", "error_msg": "Reassignment date must be before transfer date"}]}, "selling_dealer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_dealer_name_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9\\s&.,'-]{2,100}$"], "error_msgs": ["Selling dealer name is required", "Selling dealer name should be 2-100 characters with valid business name characters"], "conditionType": "AND"}, {"check": "selling_dealer_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "true", "error_msg": "Selling dealer name does not match across documents"}]}, "lien_satisfied": {"required": false, "type": "boolean", "validation_rules": [{"check": "boolean_format", "isValidationType": "EXPRESSION_TYPE", "expression": "value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0", "error_msg": "Lien satisfied should be true or false"}]}, "date_of_transfer": {"required": true, "type": "date", "validation_rules": [{"check": "transfer_date_required_transfer_date_format_and_range", "isValidationType": "REGEX_LIST", "regexes": ["^.+$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$"], "error_msgs": ["Date of transfer is required", "Date of transfer should be in MM/DD/YYYY format and between 01/01/2000 and current date"], "conditionType": "AND"}]}, "title_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_title_number_format", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]{6,20}$"], "error_msgs": ["Title number is required", "Title number should be 6-20 alphanumeric characters"], "conditionType": "AND"}]}, "dealer_fees": {"required": true, "type": "decimal", "validation_rules": [{"check": "dealer_fees_amount_range_dealer_fees_required", "isValidationType": "REGEX_LIST", "regexes": ["^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "^.+$"], "error_msgs": ["Dealer fees should be a positive amount between $0 and $99,999.99", "Dealer fees is required"], "conditionType": "AND"}]}, "expiration_date": {"required": true, "type": "date", "validation_rules": [{"check": "dl_expiration_required_dl_expiration_format_and_future_year", "isValidationType": "REGEX_LIST", "regexes": ["^.+$", "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(202[5-9]|20[3-9]\\d|21\\d{2})$"], "error_msgs": ["Driver's license expiration date is required", "Expiration date should be in MM/DD/YYYY format and must not be expired"], "conditionType": "AND"}]}, "lien_release_section": {"required": false, "type": "string", "validation_rules": [{"check": "lien_release_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,200}$", "error_msg": "Lien release section should be 2-200 characters with valid text characters"}]}, "seller_signature": {"required": true, "type": "boolean", "validation_rules": [{"check": "not_empty_signature_present", "isValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["value != None and value != \"\" and value != False", "value == True or value == \"true\" or value == 1"], "error_msgs": ["Seller signature is required", "Seller signature must be present for legal certification of transfer"], "conditionType": "AND"}]}, "signatures": {"required": true, "type": "object", "validation_rules": [{"check": "signatures_present", "isValidationType": "EXPRESSION_TYPE", "expression": "seller_signature.value != None and seller_signature.value != \"\" and buyer_signature.value != None and buyer_signature.value != \"\"", "error_msg": "Both seller and buyer signatures are required for MV-7D"}]}, "deal_status": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_deal_finalized", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(funded|accounting|finalized)$"], "error_msgs": ["Deal status is required", "Deal must be fully finalized (funded, in accounting, or finalized) before processing"], "conditionType": "AND"}]}, "title_received": {"required": true, "type": "boolean", "validation_rules": [{"check": "title_available", "isValidationType": "EXPRESSION_TYPE", "expression": "value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0", "error_msg": "Title or MSO must be physically or digitally available before processing"}]}, "reassignment_needed": {"required": false, "type": "boolean", "validation_rules": [{"check": "reassignment_check", "isValidationType": "EXPRESSION_TYPE", "expression": "vehicle_type.value != \"used\" || title_fully_assigned.value != True || mv7d_included.value != None", "error_msg": "Used vehicle with fully assigned title requires MV-7D reassignment form"}]}, "date_consistency_check": {"required": false, "type": "object", "validation_rules": [{"check": "date_sequence", "isValidationType": "EXPRESSION_TYPE", "expression": "true", "error_msg": "Transfer dates must be in chronological order across documents"}]}, "county_tax_jurisdiction": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "County tax jurisdiction is required"}, {"check": "county_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "mvl.county_of_residence.value == dealer_dmv.county_tax_jurisdiction.value", "error_msg": "County tax jurisdiction must match across MV1 and Dealer DMV records"}]}, "mv34_required": {"required": false, "type": "boolean", "validation_rules": [{"check": "mv34_trigger", "isValidationType": "EXPRESSION_TYPE", "expression": "!(address_mismatch_detected == True) || True", "error_msg": "MV-34 form required when addresses differ across documents"}]}, "title_chain_complete": {"required": true, "type": "boolean", "validation_rules": [{"check": "title_chain_validation", "isValidationType": "EXPRESSION_TYPE", "expression": "title.field.value == red_reassignment.field.value and red_reassignment.field.value == bos.field.value", "error_msg": "Title chain broken or not properly assigned between owner and dealership"}]}, "vehicle_type": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_vehicle_type_valid", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(new|used)$"], "error_msgs": ["Vehicle type is required", "Vehicle type must be 'new' or 'used'"], "conditionType": "AND"}]}, "reassignment_form_complete": {"required": false, "type": "boolean", "validation_rules": [{"check": "reassignment_completeness", "isValidationType": "EXPRESSION_TYPE", "expression": "!(vehicle_type == 'used') || True", "error_msg": "Reassignment form missing required signatures, VIN, mileage, or transfer date"}]}, "mv7d_inclusion_check": {"required": false, "type": "boolean", "validation_rules": [{"check": "mv7d_required_check", "isValidationType": "EXPRESSION_TYPE", "expression": "!(vehicle_type == 'used' && title_fully_assigned == True) || (required_field.value != None)", "error_msg": "Used vehicle with fully assigned title requires MV-7D reassignment form to be included"}]}, "title_availability_status": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty_title_ready_for_processing", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^(physically_available|digitally_available)$"], "error_msgs": ["Title availability status is required", "Title or MSO must be physically or digitally available before processing"], "conditionType": "AND"}]}, "tax_calculation_verification": {"required": true, "type": "object", "validation_rules": [{"check": "tavt_calculation_accuracy", "isValidationType": "EXPRESSION_TYPE", "expression": "abs(bos.field.value - mvl.field.value) <= 500.0 and abs(mvl.field.value - dealer_dmv.field.value) <= 500.0", "error_msg": "TAVT calculation verification failed - tax amount differs by more than $500 across documents"}]}, "address_county_validation": {"required": true, "type": "object", "validation_rules": [{"check": "address_county_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "dl.address.value == mvl.buyer_address.value and mvl.buyer_address.value == bos.buyer_address.value and bos.buyer_address.value == dealer_dmv.buyer_address.value", "error_msg": "Address or county mismatch affects TAVT calculation - all addresses must match or MV-34 required"}]}, "document_legibility_check": {"required": true, "type": "boolean", "validation_rules": [{"check": "document_readable", "isValidationType": "EXPRESSION_TYPE", "expression": "value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0", "error_msg": "Document information is illegible and cannot be processed"}]}, "tax_jurisdiction_verification": {"required": true, "type": "object", "validation_rules": [{"check": "jurisdiction_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "dl.address.value == mvl.county_of_residence.value and mvl.county_of_residence.value == dealer_dmv.county_tax_jurisdiction.value", "error_msg": "Tax jurisdiction must be consistent across driver's license address, MV1 county, and dealer DMV records"}]}, "sales_price_tax_base": {"required": true, "type": "decimal", "validation_rules": [{"check": "tax_base_calculation", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.field.value == mvl.field.value", "error_msg": "Sales price used for tax calculation must be consistent across BOS and MV1"}]}, "title_fully_assigned": {"required": false, "type": "boolean", "validation_rules": [{"check": "title_assignment_status", "isValidationType": "EXPRESSION_TYPE", "expression": "value == True or value == False or value == \"true\" or value == \"false\" or value == 1 or value == 0", "error_msg": "Title assignment status must be determined for reassignment requirements"}]}, "dealer_dmv_submission_ready": {"required": true, "type": "boolean", "validation_rules": [{"check": "submission_readiness", "isValidationType": "EXPRESSION_TYPE", "expression": "deal_finalized.value == True && title_available.value == True && all_documents_complete.value == True", "error_msg": "All conditions must be met before Dealer DMV submission"}]}}}}