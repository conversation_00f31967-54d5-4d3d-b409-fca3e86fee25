"""
Mathematical utility functions for safe operations
"""

from typing import Union, Any


class MathUtils:
    """Utility class for safe mathematical operations"""
    
    @staticmethod
    def safe_abs(value: Any) -> float:
        """
        Safe absolute value function that handles None values and string conversion
        
        Args:
            value: The value to get absolute value of
            
        Returns:
            Absolute value as float, 0 if conversion fails
        """
        if value is None:
            return 0.0
        try:
            return abs(float(value))
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def safe_subtract(a: Any, b: Any) -> float:
        """
        Safe subtraction that handles None values and string conversion
        
        Args:
            a: First operand
            b: Second operand
            
        Returns:
            Result of a - b as float, 0 if conversion fails
        """
        if a is None or b is None:
            return 0.0
        try:
            return float(a) - float(b)
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def safe_add(a: Any, b: Any) -> float:
        """
        Safe addition that handles None values and string conversion
        
        Args:
            a: First operand
            b: Second operand
            
        Returns:
            Result of a + b as float, 0 if conversion fails
        """
        if a is None or b is None:
            return 0.0
        try:
            return float(a) + float(b)
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def safe_multiply(a: Any, b: Any) -> float:
        """
        Safe multiplication that handles None values and string conversion
        
        Args:
            a: First operand
            b: Second operand
            
        Returns:
            Result of a * b as float, 0 if conversion fails
        """
        if a is None or b is None:
            return 0.0
        try:
            return float(a) * float(b)
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def safe_divide(a: Any, b: Any) -> float:
        """
        Safe division that handles None values, string conversion, and division by zero
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Result of a / b as float, 0 if conversion fails or division by zero
        """
        if a is None or b is None:
            return 0.0
        try:
            dividend = float(a)
            divisor = float(b)
            if divisor == 0:
                return 0.0
            return dividend / divisor
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def safe_compare(a: Any, b: Any, tolerance: float = 0.01) -> bool:
        """
        Safe comparison with tolerance for floating point numbers
        
        Args:
            a: First value
            b: Second value
            tolerance: Tolerance for comparison
            
        Returns:
            True if values are within tolerance, False otherwise
        """
        if a is None or b is None:
            return a == b
        try:
            val_a = float(a)
            val_b = float(b)
            return abs(val_a - val_b) <= tolerance
        except (ValueError, TypeError):
            return str(a) == str(b)
    
    @staticmethod
    def is_within_range(value: Any, min_val: Union[int, float], max_val: Union[int, float]) -> bool:
        """
        Check if value is within specified range
        
        Args:
            value: Value to check
            min_val: Minimum value (inclusive)
            max_val: Maximum value (inclusive)
            
        Returns:
            True if value is within range, False otherwise
        """
        if value is None:
            return False
        try:
            num_value = float(value)
            return min_val <= num_value <= max_val
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def safe_percentage(part: Any, whole: Any) -> float:
        """
        Calculate percentage safely
        
        Args:
            part: Part value
            whole: Whole value
            
        Returns:
            Percentage as float, 0 if calculation fails
        """
        if part is None or whole is None:
            return 0.0
        try:
            part_val = float(part)
            whole_val = float(whole)
            if whole_val == 0:
                return 0.0
            return (part_val / whole_val) * 100.0
        except (ValueError, TypeError):
            return 0.0
