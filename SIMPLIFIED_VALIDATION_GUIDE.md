# Simplified Validation System Guide

## Overview

The validation system has been simplified to use only **two validation types**:

1. **REGEX** - For pattern-based validation
2. **EXPRESSION_TYPE** - For all complex validation logic using global functions

This approach makes the code more generic and config-driven, reducing complexity while maintaining full functionality.

## Validation Types

### 1. REGEX Validation

Used for simple pattern matching and format validation.

```json
{
  "check": "alphanumeric_check",
  "isValidationType": "REGEX",
  "regex": "^[A-Za-z0-9]+$",
  "error_msg": "Value should be alphanumeric"
}
```

### 2. EXPRESSION_TYPE Validation

Used for all complex validation logic using global functions with `{{}}` placeholders.

#### Basic Expression
```json
{
  "check": "boolean_check",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "isBoolean({{value}})",
  "error_msg": "Value must be a valid boolean"
}
```

#### Multiple Conditions with AND/OR
```json
{
  "check": "multiple_conditions",
  "isValidationType": "EXPRESSION_TYPE",
  "conditionType": "AND",
  "values": [
    "isNotEmpty({{field1}})",
    "isNotEmpty({{field2}})",
    "fieldsMatch({{field1}}, {{field2}})"
  ],
  "error_msg": "Both fields must be present and match"
}
```

## Available Global Functions

### Boolean Functions
- `isBoolean(value)` - Check if value is a valid boolean
- `isTruthy(value)` - Check if value is truthy
- `isFalsy(value)` - Check if value is falsy

### String Functions
- `isNotEmpty(value)` - Check if value is not empty
- `hasLength(value, min_length, max_length)` - Check length constraints
- `contains(value, substring)` - Check if value contains substring
- `startsWith(value, prefix)` - Check if value starts with prefix
- `endsWith(value, suffix)` - Check if value ends with suffix

### Numeric Functions
- `isNumeric(value)` - Check if value is numeric
- `isInRange(value, min_value, max_value)` - Check if value is in range
- `isGreaterThan(value, compare_value)` - Check if value is greater than
- `isLessThan(value, compare_value)` - Check if value is less than

### Date Functions
- `isValidDate(value, date_format)` - Check if value is a valid date
- `isDateBefore(value, compare_date, date_format)` - Check if date is before another
- `isDateAfter(value, compare_date, date_format)` - Check if date is after another
- `isAgeInRange(value, min_age, max_age, date_format)` - Check if date represents age in range

### Cross-field Functions
- `fieldsMatch(*field_values)` - Check if all field values match
- `fieldsMatchWithTolerance(tolerance, *field_values)` - Check numeric fields match within tolerance
- `allFieldsHaveValue(*field_values)` - Check if all fields have non-empty values
- `anyFieldHasValue(*field_values)` - Check if any field has a non-empty value

### Conditional Functions
- `when(condition, then_result, else_result)` - Conditional logic
- `ifThen(condition, then_result)` - If-then logic
- `ifThenElse(condition, then_result, else_result)` - If-then-else logic

### Specialized Functions
- `isValidUSState(value)` - Check if value is a valid US state abbreviation
- `hasRequiredFields(*field_values)` - Check if all required fields have values
- `isDateSequenceValid(sequence_rule, *date_values)` - Check if dates are in proper sequence
- `isCountyConsistent(*county_values)` - Check if county values are consistent
- `isTaxCalculationValid(tolerance, *tax_values)` - Check if tax calculations are within tolerance

## Placeholder System

Use `{{}}` placeholders to reference field values:

### Current Field Value
- `{{value}}` or `{{field_value}}` - Current field being validated

### Cross-group Field References
- `{{group.field}}` - Reference field from specific group
- Examples: `{{bos.buyer_name}}`, `{{mv1.year}}`, `{{title.odometer_reading}}`

### Current Group Field References
- `{{field_name}}` - Reference field from current group

## Examples

### 1. Simple Boolean Validation
```json
{
  "check": "document_readable",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "isBoolean({{value}})",
  "error_msg": "Document legibility check must be a valid boolean value"
}
```

### 2. Cross-field Consistency
```json
{
  "check": "year_consistency",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "fieldsMatch({{bos.year}}, {{mv1.year}}, {{mv7d.year}}, {{title.year}})",
  "error_msg": "Year does not match across documents"
}
```

### 3. Numeric Tolerance Validation
```json
{
  "check": "odometer_consistency",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "fieldsMatchWithTolerance(10, {{bos.odometer_reading}}, {{mv1.odometer_reading}})",
  "error_msg": "Odometer readings must be within 10 units"
}
```

### 4. Conditional Validation
```json
{
  "check": "conditional_requirement",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "ifThen(isNotEmpty({{lien_holder}}), allFieldsHaveValue({{lien_address}}, {{lien_amount}}))",
  "error_msg": "When lien holder is specified, address and amount are required"
}
```

### 5. Multiple Conditions with AND
```json
{
  "check": "name_validation",
  "isValidationType": "EXPRESSION_TYPE",
  "conditionType": "AND",
  "values": [
    "isNotEmpty({{mvl.fullname}})",
    "isNotEmpty({{bos.fullname}})",
    "fieldsMatch({{mvl.fullname}}, {{bos.fullname}})"
  ],
  "error_msg": "Both MVL and BOS must have fullname and they must match"
}
```

### 6. Multiple Conditions with OR
```json
{
  "check": "address_requirement",
  "isValidationType": "EXPRESSION_TYPE",
  "conditionType": "OR",
  "values": [
    "isNotEmpty({{primary_address}})",
    "isNotEmpty({{secondary_address}})"
  ],
  "error_msg": "Either primary or secondary address must be provided"
}
```

### 7. Date Sequence Validation
```json
{
  "check": "date_sequence",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "isDateSequenceValid('increasing', {{date_of_reassignment}}, {{date_of_transfer}})",
  "error_msg": "Reassignment date must be before transfer date"
}
```

### 8. Age Validation
```json
{
  "check": "age_validation",
  "isValidationType": "EXPRESSION_TYPE",
  "expression": "isAgeInRange({{value}}, 16, 120)",
  "error_msg": "Age should be between 16 and 120 years"
}
```

## Benefits

1. **Simplified Code**: Only two validation types to maintain
2. **Config-Driven**: All validation logic defined in JSON configuration
3. **Generic Functions**: Reusable global functions for common validation patterns
4. **Flexible**: Can handle simple to complex validation scenarios
5. **Readable**: Clear expression syntax with meaningful function names
6. **Maintainable**: Easy to add new global functions without changing core validation logic

## Migration from Old System

Old validation types are replaced as follows:

- `NUMERIC` → Use `EXPRESSION_TYPE` with `isNumeric()`, `isInRange()`, etc.
- `DATE` → Use `EXPRESSION_TYPE` with `isValidDate()`, `isDateBefore()`, etc.
- `COUNTRY` → Use `EXPRESSION_TYPE` with `isValidUSState()`, etc.
- `BOOLEAN` → Use `EXPRESSION_TYPE` with `isBoolean()`
- `OBJECT` → Use `EXPRESSION_TYPE` with `hasRequiredFields()`, `allFieldsHaveValue()`
- `CROSS_FIELD` → Use `EXPRESSION_TYPE` with `fieldsMatch()`, `fieldsMatchWithTolerance()`
- `CONDITIONAL` → Use `EXPRESSION_TYPE` with `when()`, `ifThen()`, `ifThenElse()`
