#!/usr/bin/env python3
"""
Final test for the cleaned validation system with only 4 validation types
"""

import json
from validation_utils import ValidationUtils, ValidationType

def test_validation_types():
    """Test all 4 validation types"""
    print("=== Testing All 4 Validation Types ===")
    
    # Show available types
    types = [vt.value for vt in ValidationType]
    print(f"Available validation types: {types}")
    print()

def test_regex_type():
    """Test REGEX validation type"""
    print("=== Testing REGEX Type ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "vin_format",
                "isValidationType": "REGEX",
                "regex": "^[A-HJ-NPR-Z0-9]{17}$",
                "error_msg": "VIN must be 17 characters (excluding I, O, Q)"
            }
        ]
    }
    
    # Valid VIN
    errors = validator.validate_field("1HGBH41JXMN109186", field_config, {})
    print(f"Valid VIN: {errors}")
    
    # Invalid VIN
    errors = validator.validate_field("INVALID", field_config, {})
    print(f"Invalid VIN: {errors}")
    print()

def test_regex_list_type():
    """Test REGEX_LIST validation type"""
    print("=== Testing REGEX_LIST Type ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "deal_number_validation",
                "isValidationType": "REGEX_LIST",
                "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"],
                "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"],
                "conditionType": "AND"
            }
        ]
    }
    
    # Valid deal number
    errors = validator.validate_field("DEAL123", field_config, {})
    print(f"Valid deal number: {errors}")
    
    # Empty deal number
    errors = validator.validate_field("", field_config, {})
    print(f"Empty deal number: {errors}")
    
    # Invalid characters
    errors = validator.validate_field("DEAL-123", field_config, {})
    print(f"Invalid characters: {errors}")
    print()

def test_expression_type():
    """Test EXPRESSION_TYPE validation type"""
    print("=== Testing EXPRESSION_TYPE Type ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "vin_consistency",
                "isValidationType": "EXPRESSION_TYPE",
                "expression": "bos.vin.value == mv1.vin.value && mv1.vin.value == title.vin.value",
                "error_msg": "VIN number does not match across documents"
            }
        ]
    }
    
    # Mock data with matching VINs
    all_data = {
        "groups": {
            "bos": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
            "mv1": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
            "title": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}}
        }
    }
    
    errors = validator.validate_field("1HGBH41JXMN109186", field_config, {}, all_data, "bos", "vin")
    print(f"Matching VINs: {errors}")
    
    # Mock data with non-matching VINs
    all_data["groups"]["title"]["fields"]["vin"]["value"] = "DIFFERENT123456789"
    
    errors = validator.validate_field("1HGBH41JXMN109186", field_config, {}, all_data, "bos", "vin")
    print(f"Non-matching VINs: {errors}")
    print()

def test_expression_type_list():
    """Test EXPRESSION_TYPE_LIST validation type"""
    print("=== Testing EXPRESSION_TYPE_LIST Type ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "buyer_name_consistency",
                "isValidationType": "EXPRESSION_TYPE_LIST",
                "expressions": [
                    "bos.buyer_name.value != None && bos.buyer_name.value != \"\"",
                    "dl.full_name.value != None && dl.full_name.value != \"\"",
                    "bos.buyer_name.value == dl.full_name.value"
                ],
                "error_msgs": [
                    "BOS buyer name is required",
                    "DL full name is required",
                    "Buyer names must match"
                ],
                "conditionType": "AND"
            }
        ]
    }
    
    # Mock data with all conditions met
    all_data = {
        "groups": {
            "bos": {"fields": {"buyer_name": {"value": "John Doe"}}},
            "dl": {"fields": {"full_name": {"value": "John Doe"}}}
        }
    }
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"All conditions met: {errors}")
    
    # Mock data with missing DL name
    all_data["groups"]["dl"]["fields"]["full_name"]["value"] = ""
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"Missing DL name: {errors}")
    print()

def test_collection_data_integration():
    """Test integration with converted collection_data.json"""
    print("=== Testing Collection Data Integration ===")
    
    try:
        with open('collection_data.json', 'r') as f:
            collection_data = json.load(f)
        
        validator = ValidationUtils()
        
        # Count validation types in collection data
        type_counts = {"REGEX": 0, "REGEX_LIST": 0, "EXPRESSION_TYPE": 0, "EXPRESSION_TYPE_LIST": 0, "OTHER": 0}
        
        for field_name, field_config in collection_data["properties"]["fields"].items():
            validation_rules = field_config.get("validation_rules", [])
            for rule in validation_rules:
                validation_type = rule.get("isValidationType", "")
                if validation_type in type_counts:
                    type_counts[validation_type] += 1
                else:
                    type_counts["OTHER"] += 1
        
        print(f"Validation type distribution in collection_data.json:")
        for vtype, count in type_counts.items():
            print(f"  {vtype}: {count}")
        
        # Test a few fields from collection data
        print("\nTesting specific fields:")
        
        # Test deal_number (should be REGEX_LIST)
        deal_number_config = collection_data["properties"]["fields"]["deal_number"]
        errors = validator.validate_field("DEAL123", deal_number_config, {})
        print(f"Deal number validation: {errors}")
        
        # Test VIN (should be EXPRESSION_TYPE)
        vin_config = collection_data["properties"]["fields"]["vin"]
        all_data = {
            "groups": {
                "bos": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
                "mv1": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
                "mv7d": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
                "title": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}}
            }
        }
        errors = validator.validate_field("1HGBH41JXMN109186", vin_config, {}, all_data, "bos", "vin")
        print(f"VIN validation: {errors}")
        
        print("Collection data integration: SUCCESS")
        
    except Exception as e:
        print(f"Collection data integration: FAILED - {str(e)}")
    
    print()

def test_invalid_validation_type():
    """Test handling of invalid validation types"""
    print("=== Testing Invalid Validation Type Handling ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "invalid_type",
                "isValidationType": "INVALID_TYPE",
                "error_msg": "This should fail"
            }
        ]
    }
    
    errors = validator.validate_field("test", field_config, {})
    print(f"Invalid validation type result: {errors}")
    print()

def main():
    """Run all tests"""
    print("Testing Final Clean Validation System")
    print("=" * 50)
    
    test_validation_types()
    test_regex_type()
    test_regex_list_type()
    test_expression_type()
    test_expression_type_list()
    test_collection_data_integration()
    test_invalid_validation_type()
    
    print("All tests completed successfully!")
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print("✅ Only 4 validation types: REGEX, REGEX_LIST, EXPRESSION_TYPE, EXPRESSION_TYPE_LIST")
    print("✅ All old types (BOOLEAN, CROSS_FIELD, CONDITIONAL) converted to new types")
    print("✅ Enum-based validation type checking")
    print("✅ Clean, minimal validation_utils.py")
    print("✅ Updated collection_data.json with new validation types")

if __name__ == "__main__":
    main()
