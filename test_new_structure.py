#!/usr/bin/env python3
"""
Test script to demonstrate the new validation system structure
"""

import json
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from app.core.validation_engine import ValidationEngine
from app.core.enums import ValidationType
from app.utils.math_utils import MathUtils


def test_new_validation_structure():
    """Test the new validation system structure"""
    
    print("=== Testing New Validation System Structure ===\n")
    
    # Load the collection data
    with open('collection_data.json', 'r') as f:
        config = json.load(f)
    
    # Initialize validation engine
    validation_engine = ValidationEngine()
    
    # Test data
    test_data = {
        "groups": {
            "bos": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_name": {"value": "John Doe"},
                    "odometer_reading": {"value": 50000},
                    "tavt_tax_amount": {"value": 1500.00}
                }
            },
            "mvl": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_full_name": {"value": "<PERSON>"},
                    "odometer_reading": {"value": 50005},
                    "tavt_tax_amount": {"value": 1450.00}
                }
            },
            "title": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_name": {"value": "John Doe"},
                    "odometer_reading": {"value": 50002}
                }
            },
            "red_reassignment": {
                "fields": {
                    "vin": {"value": "1HGBH41JXMN109186"},
                    "buyer_name": {"value": "John Doe"},
                    "odometer_reading": {"value": 50001}
                }
            },
            "dl": {
                "fields": {
                    "full_name": {"value": "John Doe"}
                }
            },
            "dealer_dmv": {
                "fields": {
                    "tavt_tax_amount": {"value": 1480.00}
                }
            }
        }
    }
    
    print("1. Testing ValidationEngine with VIN validation...")
    vin_field_config = config["properties"]["fields"]["vin"]
    vin_errors = validation_engine.validate_field(
        "1HGBH41JXMN109186", vin_field_config, config, test_data, "bos", "vin"
    )
    print(f"   VIN validation errors: {vin_errors}")
    
    print("\n2. Testing ValidationEngine with buyer name validation...")
    buyer_name_config = config["properties"]["fields"]["buyer_name"]
    buyer_name_errors = validation_engine.validate_field(
        "John Doe", buyer_name_config, config, test_data, "bos", "buyer_name"
    )
    print(f"   Buyer name validation errors: {buyer_name_errors}")
    
    print("\n3. Testing ValidationEngine with odometer validation...")
    odometer_config = config["properties"]["fields"]["odometer_reading"]
    odometer_errors = validation_engine.validate_field(
        50000, odometer_config, config, test_data, "bos", "odometer_reading"
    )
    print(f"   Odometer validation errors: {odometer_errors}")
    
    print("\n4. Testing MathUtils directly...")
    math_utils = MathUtils()
    print(f"   safe_abs(-5): {math_utils.safe_abs(-5)}")
    print(f"   safe_abs(None): {math_utils.safe_abs(None)}")
    print(f"   safe_subtract(100, 95): {math_utils.safe_subtract(100, 95)}")
    print(f"   safe_subtract(None, 5): {math_utils.safe_subtract(None, 5)}")
    
    print("\n5. Testing ValidationType enum...")
    print(f"   Available validation types: {[vt.value for vt in ValidationType]}")
    
    print("\n=== New Structure Test Completed Successfully! ===")
    print("\nBenefits of the new structure:")
    print("✅ Modular design with clear separation of concerns")
    print("✅ Easy to extend with new validation types")
    print("✅ Better testability with isolated components")
    print("✅ Professional folder structure")
    print("✅ Reusable utility functions")
    print("✅ Type hints and documentation")


if __name__ == "__main__":
    test_new_validation_structure()
