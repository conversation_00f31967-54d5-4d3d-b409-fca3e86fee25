"""
Database manager for handling different database types
"""

import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import json


class DatabaseManager:
    """Database manager that supports multiple database types"""
    
    def __init__(self):
        """Initialize database manager with environment configuration"""
        self.db_type = os.getenv('DB_TYPE', 'mongodb').lower()
        self.connection = None
        self._connect()
    
    def _connect(self):
        """Connect to the database based on DB_TYPE"""
        if self.db_type == 'mongodb':
            self._connect_mongodb()
        elif self.db_type == 'postgresql':
            self._connect_postgresql()
        elif self.db_type == 'mysql':
            self._connect_mysql()
        else:
            raise ValueError(f"Unsupported database type: {self.db_type}")
    
    def _connect_mongodb(self):
        """Connect to MongoDB"""
        try:
            from pymongo import MongoClient
            
            mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
            self.client = MongoClient(mongo_uri)
            self.db = self.client[os.getenv('MONGO_DB_NAME', 'tag_title')]
            self.collection = self.db[os.getenv('MONGO_COLLECTION_NAME', 'json_storage')]
            print(f"Connected to MongoDB: {mongo_uri}")
            
        except ImportError:
            raise ImportError("pymongo is required for MongoDB connection. Install with: pip install pymongo")
        except Exception as e:
            raise ConnectionError(f"Failed to connect to MongoDB: {str(e)}")
    
    def _connect_postgresql(self):
        """Connect to PostgreSQL"""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor
            
            connection_params = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': os.getenv('DB_PORT', '5432'),
                'database': os.getenv('DB_NAME', 'validation_db'),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD')
            }
            
            self.connection = psycopg2.connect(**connection_params)
            self.cursor = self.connection.cursor(cursor_factory=RealDictCursor)
            print(f"Connected to PostgreSQL: {connection_params['host']}:{connection_params['port']}")
            
        except ImportError:
            raise ImportError("psycopg2 is required for PostgreSQL connection. Install with: pip install psycopg2-binary")
        except Exception as e:
            raise ConnectionError(f"Failed to connect to PostgreSQL: {str(e)}")
    
    def _connect_mysql(self):
        """Connect to MySQL"""
        try:
            import mysql.connector
            
            connection_params = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': os.getenv('DB_PORT', '3306'),
                'database': os.getenv('DB_NAME', 'validation_db'),
                'user': os.getenv('DB_USER'),
                'password': os.getenv('DB_PASSWORD')
            }
            
            self.connection = mysql.connector.connect(**connection_params)
            self.cursor = self.connection.cursor(dictionary=True)
            print(f"Connected to MySQL: {connection_params['host']}:{connection_params['port']}")
            
        except ImportError:
            raise ImportError("mysql-connector-python is required for MySQL connection. Install with: pip install mysql-connector-python")
        except Exception as e:
            raise ConnectionError(f"Failed to connect to MySQL: {str(e)}")
    
    def get_validation_rules(self, rule_type: str = None) -> Dict[str, Any]:
        """
        Get validation rules from database
        
        Args:
            rule_type: Optional filter for specific rule type
            
        Returns:
            Dictionary containing validation rules
        """
        if self.db_type == 'mongodb':
            return self._get_validation_rules_mongodb(rule_type)
        else:
            return self._get_validation_rules_sql(rule_type)
    
    def _get_validation_rules_mongodb(self, rule_type: str = None) -> Dict[str, Any]:
        """Get validation rules from MongoDB"""
        try:
            query = {}
            if rule_type:
                query['validation_type_key'] = rule_type
            
            result = self.collection.find_one(query)
            if result:
                # Remove MongoDB's _id field
                result.pop('_id', None)
                return result
            else:
                return {}
                
        except Exception as e:
            print(f"Error fetching validation rules from MongoDB: {str(e)}")
            return {}
    
    def _get_validation_rules_sql(self, rule_type: str = None) -> Dict[str, Any]:
        """Get validation rules from SQL database"""
        try:
            query = "SELECT * FROM validation_rules"
            params = []
            
            if rule_type:
                query += " WHERE rule_type = %s"
                params.append(rule_type)
            
            self.cursor.execute(query, params)
            results = self.cursor.fetchall()
            
            if results:
                # Convert to the expected format
                return {
                    'validation_type_key': results[0].get('rule_type', 'default'),
                    'properties': json.loads(results[0].get('properties', '{}'))
                }
            else:
                return {}
                
        except Exception as e:
            print(f"Error fetching validation rules from SQL database: {str(e)}")
            return {}
    
    def save_validation_result(self, worksheet_id: str, validation_results: List[Dict[str, Any]]) -> bool:
        """
        Save validation results to database
        
        Args:
            worksheet_id: Unique identifier for the worksheet
            validation_results: List of validation results
            
        Returns:
            True if successful, False otherwise
        """
        if self.db_type == 'mongodb':
            return self._save_validation_result_mongodb(worksheet_id, validation_results)
        else:
            return self._save_validation_result_sql(worksheet_id, validation_results)
    
    def _save_validation_result_mongodb(self, worksheet_id: str, validation_results: List[Dict[str, Any]]) -> bool:
        """Save validation results to MongoDB"""
        try:
            document = {
                'worksheet_id': worksheet_id,
                'validation_results': validation_results,
                'created_at': datetime.utcnow(),
                'status': 'completed'
            }
            
            result = self.collection.insert_one(document)
            return result.inserted_id is not None
            
        except Exception as e:
            print(f"Error saving validation results to MongoDB: {str(e)}")
            return False
    
    def _save_validation_result_sql(self, worksheet_id: str, validation_results: List[Dict[str, Any]]) -> bool:
        """Save validation results to SQL database"""
        try:
            query = """
                INSERT INTO validation_results (worksheet_id, results, created_at, status)
                VALUES (%s, %s, %s, %s)
            """
            
            params = (
                worksheet_id,
                json.dumps(validation_results),
                datetime.utcnow(),
                'completed'
            )
            
            self.cursor.execute(query, params)
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"Error saving validation results to SQL database: {str(e)}")
            return False
    
    def close(self):
        """Close database connection"""
        try:
            if self.db_type == 'mongodb' and hasattr(self, 'client'):
                self.client.close()
            elif hasattr(self, 'connection') and self.connection:
                if hasattr(self, 'cursor') and self.cursor:
                    self.cursor.close()
                self.connection.close()
            print("Database connection closed")
        except Exception as e:
            print(f"Error closing database connection: {str(e)}")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
