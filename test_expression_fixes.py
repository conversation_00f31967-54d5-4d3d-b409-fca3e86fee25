#!/usr/bin/env python3
"""
Test script to verify the expression evaluation fixes
"""

from validation_utils import ValidationUtils

def test_null_handling():
    """Test null value handling in expressions"""
    print("=== Testing Null Value Handling ===")
    
    validator = ValidationUtils()
    
    # Test abs with null values
    result = validator._execute_direct_expression("abs(null - null) <= 10")
    print(f"abs(null - null) <= 10: {result}")
    
    # Test abs with mixed null and number
    result = validator._execute_direct_expression('abs(null - "42797") <= 10')
    print(f'abs(null - "42797") <= 10: {result}')
    
    # Test complex expression with nulls
    result = validator._execute_direct_expression("abs(null - null) <= 10 && abs(null - null) <= 10 && abs(null - \"42797\") <= 10")
    print(f"Complex null expression: {result}")
    
    print()

def test_boolean_handling():
    """Test boolean value handling"""
    print("=== Testing Boolean Value Handling ===")
    
    validator = ValidationUtils()
    
    # Test true/false conversion
    result = validator._execute_direct_expression("true")
    print(f"true: {result}")
    
    result = validator._execute_direct_expression("false")
    print(f"false: {result}")
    
    # Test boolean in complex expression
    result = validator._execute_direct_expression('"Julie" != null && "Julie" != "" && "Julie" != false')
    print(f'Complex boolean expression: {result}')
    
    print()

def test_field_value_formatting():
    """Test field value formatting"""
    print("=== Testing Field Value Formatting ===")
    
    validator = ValidationUtils()
    
    # Test various value types
    test_values = [None, "", "test", True, False, 123, 45.67]
    
    for value in test_values:
        formatted = validator._format_value_for_direct_expression(value)
        print(f"Value: {value} -> Formatted: {formatted}")
    
    print()

def test_safe_math_functions():
    """Test safe mathematical functions"""
    print("=== Testing Safe Math Functions ===")
    
    validator = ValidationUtils()
    
    # Test safe_abs
    test_values = [None, "", "123", "-45", "abc", 67]
    for value in test_values:
        result = validator._safe_abs(value)
        print(f"safe_abs({value}): {result}")
    
    print()
    
    # Test safe_subtract
    test_pairs = [(None, None), (None, 10), (20, None), ("30", "15"), ("abc", "def"), (100, 25)]
    for a, b in test_pairs:
        result = validator._safe_subtract(a, b)
        print(f"safe_subtract({a}, {b}): {result}")
    
    print()

def test_expression_preprocessing():
    """Test expression preprocessing for null safety"""
    print("=== Testing Expression Preprocessing ===")
    
    validator = ValidationUtils()
    
    test_expressions = [
        "abs(a - b) <= 10",
        "abs(field1.value - field2.value) <= 500",
        "abs(null - null) <= 10 && abs(value1 - value2) <= 5",
        "normal_expression == true"
    ]
    
    for expr in test_expressions:
        processed = validator._preprocess_expression_for_null_safety(expr)
        print(f"Original: {expr}")
        print(f"Processed: {processed}")
        print()

def test_real_validation_scenarios():
    """Test real validation scenarios that were failing"""
    print("=== Testing Real Validation Scenarios ===")
    
    validator = ValidationUtils()
    
    # Mock data similar to what was causing errors
    all_data = {
        "groups": {
            "bos": {"fields": {"odometer_reading": {"value": None}}},
            "mv1": {"fields": {"odometer_reading": {"value": None}}},
            "title": {"fields": {"odometer_reading": {"value": "42797"}}}
        }
    }
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "odometer_consistency_odometer_title_exception",
                "isValidationType": "EXPRESSION_TYPE_LIST",
                "expressions": [
                    "abs(bos.odometer_reading.value - mv1.odometer_reading.value) <= 10 && abs(mv1.odometer_reading.value - mv7d.odometer_reading.value) <= 10 && abs(mv7d.odometer_reading.value - title.odometer_reading.value) <= 10",
                    "title.odometer_reading.value <= bos.odometer_reading.value"
                ],
                "error_msgs": [
                    "Odometer reading does not match across documents",
                    "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"
                ],
                "conditionType": "AND"
            }
        ]
    }
    
    errors = validator.validate_field(None, field_config, {}, all_data, "bos", "odometer_reading")
    print(f"Odometer validation with nulls: {errors}")
    
    # Test boolean validation
    field_config_bool = {
        "required": True,
        "validation_rules": [
            {
                "check": "address_consistency",
                "isValidationType": "EXPRESSION_TYPE",
                "expression": "true",
                "error_msg": "Address consistency check disabled"
            }
        ]
    }
    
    errors = validator.validate_field("test", field_config_bool, {})
    print(f"Boolean 'true' validation: {errors}")
    
    print()

def main():
    """Run all tests"""
    print("Testing Expression Evaluation Fixes")
    print("=" * 50)
    
    test_null_handling()
    test_boolean_handling()
    test_field_value_formatting()
    test_safe_math_functions()
    test_expression_preprocessing()
    test_real_validation_scenarios()
    
    print("All tests completed!")

if __name__ == "__main__":
    main()
