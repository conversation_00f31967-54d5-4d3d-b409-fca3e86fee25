# Validation System - New Structure

This document describes the new, professional folder structure for the validation system.

## Folder Structure

```
app/
├── __init__.py
├── core/
│   ├── __init__.py
│   ├── enums.py                    # Validation type enumerations
│   ├── constants.py                # System constants and configurations
│   └── validation_engine.py        # Main validation orchestrator
├── validators/
│   ├── __init__.py
│   ├── base_validator.py           # Abstract base validator class
│   ├── regex_validator.py          # Regex validation implementation
│   └── expression_validator.py     # Expression validation implementation
└── utils/
    ├── __init__.py
    └── math_utils.py               # Safe mathematical operations
```

## Components Overview

### Core Components

#### `ValidationEngine`
- Main orchestrator that coordinates all validation operations
- Routes validation requests to appropriate validators
- Handles field-level validation logic
- Manages validation rule processing

#### `ValidationType` (Enum)
- Defines supported validation types:
  - `REGEX`: Single regex pattern validation
  - `REGEX_LIST`: Multiple regex patterns with AND/OR logic
  - `EXPRESSION_TYPE`: Single Python expression validation
  - `EXPRESSION_TYPE_LIST`: Multiple expressions with AND/OR logic

#### `ValidationConstants`
- Centralized constants and configuration values
- Error message templates
- Default values and thresholds
- Regex patterns for field references

### Validators

#### `BaseValidator`
- Abstract base class for all validators
- Provides common functionality:
  - Error message handling
  - Condition type processing (AND/OR)
  - Result aggregation logic

#### `RegexValidator`
- Handles regex-based validation
- Supports single and multiple regex patterns
- Implements AND/OR logic for multiple patterns

#### `ExpressionValidator`
- Handles Python expression validation
- Supports field references and cross-document validation
- Safe expression evaluation with restricted scope
- Mathematical operations with None-safe functions

### Utilities

#### `MathUtils`
- Safe mathematical operations that handle None values
- Functions include:
  - `safe_abs()`: Safe absolute value
  - `safe_subtract()`: Safe subtraction
  - `safe_add()`: Safe addition
  - `safe_multiply()`: Safe multiplication
  - `safe_divide()`: Safe division with zero-check
  - `safe_compare()`: Tolerance-based comparison
  - `is_within_range()`: Range validation
  - `safe_percentage()`: Percentage calculation

## Usage Example

```python
from app.core.validation_engine import ValidationEngine
from app.utils.math_utils import MathUtils

# Initialize validation engine
validation_engine = ValidationEngine()

# Validate a field
errors = validation_engine.validate_field(
    field_value="1HGBH41JXMN109186",
    field_config=vin_config,
    config=global_config,
    all_data=document_data,
    current_group="bos",
    current_field="vin"
)

# Use math utilities
math_utils = MathUtils()
result = math_utils.safe_subtract(100, 95)  # Returns 5.0
```

## Benefits of New Structure

1. **Modularity**: Each component has a single responsibility
2. **Extensibility**: Easy to add new validation types or utilities
3. **Testability**: Components can be tested in isolation
4. **Maintainability**: Clear organization makes code easier to understand
5. **Reusability**: Utilities can be used across different parts of the system
6. **Type Safety**: Full type hints for better IDE support and error detection
7. **Documentation**: Comprehensive docstrings for all methods

## Migration from Old Structure

The new structure maintains the same API as the old `ValidationUtils` class, but with improved organization:

- `ValidationUtils.validate_field()` → `ValidationEngine.validate_field()`
- `ValidationUtils._safe_abs()` → `MathUtils.safe_abs()`
- `ValidationUtils._safe_subtract()` → `MathUtils.safe_subtract()`

## Testing

Run the test script to verify the new structure:

```bash
python test_new_structure.py
```

This will demonstrate all components working together with the existing configuration files.
