"""
Data models for database operations
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
import json


@dataclass
class ValidationResult:
    """Model for validation result data"""
    field_name: str
    field_value: Any
    is_valid: bool
    errors: List[str]
    group_name: str
    validation_type: str
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationResult':
        """Create instance from dictionary"""
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class WorksheetData:
    """Model for worksheet data"""
    worksheet_id: str
    groups: Dict[str, Dict[str, Any]]
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        result = asdict(self)
        result['created_at'] = self.created_at.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorksheetData':
        """Create instance from dictionary"""
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)
    
    @classmethod
    def from_json_file(cls, file_path: str, worksheet_id: str = None) -> 'WorksheetData':
        """Create instance from JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if worksheet_id is None:
                worksheet_id = f"worksheet_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            # Extract groups data
            groups = data.get('groups', {})
            
            # Extract metadata
            metadata = {
                'source_file': file_path,
                'total_groups': len(groups),
                'total_fields': sum(len(group.get('fields', {})) for group in groups.values())
            }
            
            return cls(
                worksheet_id=worksheet_id,
                groups=groups,
                metadata=metadata
            )
            
        except Exception as e:
            raise ValueError(f"Error loading worksheet data from {file_path}: {str(e)}")
    
    def get_field_value(self, group_name: str, field_name: str) -> Any:
        """Get field value from worksheet data"""
        try:
            return self.groups[group_name]['fields'][field_name]['value']
        except KeyError:
            return None
    
    def get_all_fields(self) -> List[Dict[str, Any]]:
        """Get all fields from all groups"""
        fields = []
        for group_name, group_data in self.groups.items():
            for field_name, field_data in group_data.get('fields', {}).items():
                fields.append({
                    'group_name': group_name,
                    'field_name': field_name,
                    'field_value': field_data.get('value'),
                    'field_data': field_data
                })
        return fields


@dataclass
class ValidationSession:
    """Model for validation session data"""
    session_id: str
    worksheet_id: str
    validation_config: Dict[str, Any]
    results: List[ValidationResult]
    status: str = "pending"  # pending, running, completed, failed
    started_at: datetime = None
    completed_at: datetime = None
    error_message: str = None
    
    def __post_init__(self):
        if self.started_at is None:
            self.started_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage"""
        result = asdict(self)
        result['started_at'] = self.started_at.isoformat() if self.started_at else None
        result['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        result['results'] = [r.to_dict() for r in self.results]
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationSession':
        """Create instance from dictionary"""
        if 'started_at' in data and data['started_at']:
            data['started_at'] = datetime.fromisoformat(data['started_at'])
        if 'completed_at' in data and data['completed_at']:
            data['completed_at'] = datetime.fromisoformat(data['completed_at'])
        if 'results' in data:
            data['results'] = [ValidationResult.from_dict(r) for r in data['results']]
        return cls(**data)
    
    def add_result(self, result: ValidationResult):
        """Add validation result to session"""
        self.results.append(result)
    
    def mark_completed(self):
        """Mark session as completed"""
        self.status = "completed"
        self.completed_at = datetime.utcnow()
    
    def mark_failed(self, error_message: str):
        """Mark session as failed"""
        self.status = "failed"
        self.error_message = error_message
        self.completed_at = datetime.utcnow()
    
    def get_summary(self) -> Dict[str, Any]:
        """Get validation session summary"""
        total_validations = len(self.results)
        failed_validations = sum(1 for r in self.results if not r.is_valid)
        
        return {
            'session_id': self.session_id,
            'worksheet_id': self.worksheet_id,
            'status': self.status,
            'total_validations': total_validations,
            'failed_validations': failed_validations,
            'success_rate': (total_validations - failed_validations) / total_validations * 100 if total_validations > 0 else 0,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration_seconds': (self.completed_at - self.started_at).total_seconds() if self.completed_at and self.started_at else None
        }
