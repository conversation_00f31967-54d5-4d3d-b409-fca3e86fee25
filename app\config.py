"""
Configuration management using environment variables
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class that loads settings from environment variables"""
    
    # Database Configuration
    DB_TYPE = os.getenv('DB_TYPE', 'mongodb')
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', '27017'))
    DB_NAME = os.getenv('DB_NAME', 'tag_title')
    DB_USER = os.getenv('DB_USER', '')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    
    # MongoDB Specific
    MONGO_URI = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
    MONGO_DB_NAME = os.getenv('MONGO_DB_NAME', 'tag_title')
    MONGO_COLLECTION_NAME = os.getenv('MONGO_COLLECTION_NAME', 'json_storage')
    
    # Application Configuration
    APP_ENV = os.getenv('APP_ENV', 'development')
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    OUTPUT_DIR = os.getenv('OUTPUT_DIR', 'output')
    TEMP_DIR = os.getenv('TEMP_DIR', 'temp')
    
    # Validation Configuration
    MAX_VALIDATION_ERRORS = int(os.getenv('MAX_VALIDATION_ERRORS', '100'))
    VALIDATION_TIMEOUT = int(os.getenv('VALIDATION_TIMEOUT', '30'))
    ENABLE_CROSS_VALIDATION = os.getenv('ENABLE_CROSS_VALIDATION', 'true').lower() == 'true'
    
    # File Processing
    MAX_FILE_SIZE_MB = int(os.getenv('MAX_FILE_SIZE_MB', '50'))
    ALLOWED_FILE_TYPES = os.getenv('ALLOWED_FILE_TYPES', 'json,xlsx,csv').split(',')
    
    @classmethod
    def get_db_connection_string(cls):
        """Get database connection string based on DB_TYPE"""
        if cls.DB_TYPE.lower() == 'mongodb':
            return cls.MONGO_URI
        elif cls.DB_TYPE.lower() == 'postgresql':
            return f"postgresql://{cls.DB_USER}:{cls.DB_PASSWORD}@{cls.DB_HOST}:{cls.DB_PORT}/{cls.DB_NAME}"
        elif cls.DB_TYPE.lower() == 'mysql':
            return f"mysql://{cls.DB_USER}:{cls.DB_PASSWORD}@{cls.DB_HOST}:{cls.DB_PORT}/{cls.DB_NAME}"
        else:
            raise ValueError(f"Unsupported database type: {cls.DB_TYPE}")
    
    @classmethod
    def ensure_directories(cls):
        """Ensure output and temp directories exist"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        os.makedirs(cls.TEMP_DIR, exist_ok=True)
